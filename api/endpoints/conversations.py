import json

from fastapi import Depends, APIRouter
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>
from sqlalchemy.orm import Session
from starlette.requests import Request

from api.schemas import QueryRequest, SliceMchQueryRequest
from api.utils import get_user_roles_by_user_id

from core.agents.recommend_agent import get_recommend_prompt
from core.config.logging import logger
from core.database.repositories import get_user_chat_map, get_chat_history, delete_user_chat
from core.database.session import get_db
from core.services.query_processor import handle_user_query, process_slice_mch

conversations_router = APIRouter(prefix="/api/v2/conversations", tags=["会话管理"])


@conversations_router.post("/assistant")
async def chat(request_data: QueryRequest, request: Request):
    query = request_data.messages.strip()
    session_id = request_data.uuid.strip()
    user_id = request_data.userId.strip()
    assistant_name = request_data.assistant_name

    # 获取用户角色
    roles = get_user_roles_by_user_id(user_id)

    logger.info(f"用户编号: {user_id}；用户问题: {query} ；助手名称：{assistant_name}")

    try:
        return await handle_user_query(query, session_id, user_id, roles, assistant_name, request)

    except ValueError as e:
        logger.error(f"Chat request failed - session_id: {session_id}, error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@conversations_router.post("/slice_machine")
async def chat_slicemch(request_data: SliceMchQueryRequest, request: Request):
    label = request_data.label.strip()
    value = request_data.value.strip()
    user_id = request_data.userId.strip()
    roles = request_data.roles
    machine_no = request_data.machineNo.strip()

    logger.info(
        f"Received slice machine chat request - user_id: {user_id}, label: {label}, "
        f"value: {value}, machine_no: {machine_no}, roles: {roles}")

    try:
        return await process_slice_mch(label, value, user_id, roles, machine_no, request)

    except ValueError as e:
        logger.error(f"Slice machine chat request failed - user_id: {user_id}, error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@conversations_router.get("/{user_id}/chat_summary")
def read_user(
        user_id: str,
        db: Session = Depends(get_db)
):
    """
    读取指定用户的聊天记录摘要

    根据用户ID获取该用户的聊天记录摘要。如果用户没有聊天记录，则返回空列表。

    参数:
    - user_id: str类型，用户的唯一标识符。
    - db: Session类型，数据库会话，通过依赖注入获得。

    返回:
    - json_data: 包含用户聊天记录摘要的列表，每个摘要是一个字典，包含会话ID（uuid）和聊天记录的简短描述（desc）。
    """
    result = get_user_chat_map(db=db, user_id=user_id)
    if not result:
        return []

    json_data = []
    for r in result:
        _uuid = r.session_id
        assistant_name = r.assistant_name
        chat_history = get_chat_history(db=db, session_id=_uuid)
        if chat_history:
            decs = chat_history[0].message["data"]["content"][:20].replace("/no_think", "").replace("/think", "")
            json_data.append({"uuid": str(_uuid), "desc": decs, "assistant_name": assistant_name})
    return json_data


@conversations_router.get("/{session_id}/messages")
def read_session(session_id: str, db: Session = Depends(get_db)):
    """
    根据会话ID获取聊天记录

    参数:
    - session_id: 会话ID，用于标识特定的会话。
    - db: 数据库会话，用于执行数据库操作。

    返回:
    - 会话的聊天历史记录，以JSON格式返回。
    """
    result = get_chat_history(db=db, session_id=session_id)
    if not result:
        return []

    json_data = []
    for r in result:
        decs = r.message["data"]["content"]
        typ = r.message["data"]["type"]

        if "query" in decs and "fig" in decs:
            decs = eval(decs)
            json_data.append({"content": decs["query"], "type": typ, "fig": decs["fig"]})

        elif "query" in decs and "url" in decs:
            decs = eval(decs)
            # json_data.append({"content": decs["query"], "type": typ, "url": decs["url"]})
            json_data.append({"content": decs["query"], "type": typ})

        elif "function_call" in decs:
            decs = json.loads(decs)
            json_data.append({"content": decs[-1]["content"], "type": typ, "fig": ""})

        elif "Qwen3 Tool-calling" in decs:
            decs = json.loads(decs)
            json_data.append({"content": decs[-1]["content"], "type": typ, "fig": ""})

        else:
            if typ == "ai":
                decs = json.loads(decs)
                json_data.append({"think_part": decs["think_part"], "content": decs["answer"], "type": typ, "fig": ""})
            else:
                json_data.append({"content": decs, "type": typ, "fig": ""})

    return json_data


@conversations_router.delete("/{session_id}")
def delete_session(session_id: str, db: Session = Depends(get_db)):
    """
    删除指定的会话。

    本函数通过接收会话ID，并调用delete_user_chat函数来删除用户聊天会话。
    成功删除后，返回成功状态和删除会话的ID。

    参数:
    - session_id (str): 要删除的会话ID，通过URL路径参数传递。
    - db (Session): 数据库会话，通过依赖注入获得。

    返回:
    - 一个包含状态和消息的字典，表示会话删除成功。
    """
    delete_user_chat(db=db, session_id=session_id)
    return {"status": "success", "message": f"会话 {session_id} 已成功删除"}


@conversations_router.get("/recommend-prompt/{session_id}")
def recommend_prompt(session_id: str):
    """
    根据最后一次对话生成下一步问题建议
    参数:
    - session_id (str): 要推荐提示的会话ID，通过URL路径参数传递。
    - db (Session): 数据库会话，通过依赖注入获得。

    返回:
    - 一个包含下一步问题建议的列表
    """
    return get_recommend_prompt(session_id)

if __name__ == '__main__':
    db = next(get_db())
    read_session("267419cb-a9e1-4e68-9f0b-7258b124020d", db)