from pydantic import BaseModel, Field


class QueryRequestBasic(BaseModel):
    messages: str  # 用户传入的查询内容
    uuid: str  # 会话ID
    userId: str  # 用户工号


class QueryRequest(BaseModel):
    messages: str  # 用户传入的查询内容
    uuid: str  # 会话ID
    deepSeek: bool  # 是否启用深度思考
    userId: str  # 用户工号

    # roles: list  # 角色权限
    # [{'id': 1057, 'name': '知识库-设施'},
    # {'id': 1, 'name': '超级管理员'},
    # {'id': 2, 'name': '普通角色', # 'name': '粘胶工序'},
    # {'id': 1002, 'name': '切片工序'},
    # {'id': 1003, 'name': '脱胶工序'},
    # {'id': 1004, 'name': '插片工序'},
    # {'id': 1005, 'name': '清洗工序'},
    # {'id': 10 {'id': 1054, 'name': '推送服务'}]

    person: bool  # 个人知识库
    knowledge: bool  # 公共知识库
    sql: bool  # 数据检索
    dataBI: bool = Field(False)  # 项目管理ChatBI

    dify_agv: bool = Field(False)

    dify_slot_recommend: bool = Field(False)

    dify_project_manner: bool = Field(False)

    dify_person_eval: bool = Field(False)

    dify_meeting_minutes: bool = Field(False)


class UploadRequest(BaseModel):
    userId: str
    fileUrl: str
    fileId: int
    filName: str
    domain: str


class SliceMchQueryRequest(BaseModel):
    label: str  # 问题
    value: str  # 问题表示
    userId: str  # 工号
    # roles: list  # 角色权限
    machineNo: str  # 机台号
