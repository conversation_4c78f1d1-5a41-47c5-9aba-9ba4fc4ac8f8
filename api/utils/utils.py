import os

import psycopg2
import requests


def get_user_roles_by_user_id(user_id):
    """
    根据用户工号获取用户的权限信息。

    参数:
    job_number (str): 用户工号，例如 "D0023"。

    返回:
    list: 包含用户权限信息的列表，如果请求失败则返回空列表。
    """
    url = f"http://172.27.9.18:6998/admin-api/system/permission/list-user-roles-by-job-number?jobNumber={user_id}"
    headers = {}

    try:
        response = requests.request("GET", url, headers=headers)
        response.raise_for_status()  # 如果响应状态码不是200，抛出异常
        data = response.json()  # 解析JSON响应
        return data.get("data", [])  # 返回data部分，如果不存在则返回空列表
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return []


def get_names_by_domain(domains):
    """
    输入: domains (list) 例 ['知识库-研发中心项目月会纪要', '知识库-项目管理部组会纪要']
    输出: dict，key为domain，value为name列表
    """
    # 数据库连接信息
    conn = psycopg2.connect(
        host='***********',
        port=5432,
        dbname='gc_es',
        user='postgres',
        password='cdData01_PG0419'
    )
    cursor = conn.cursor()
    sql = """
    SELECT name, url FROM public.es_person_knowledge_file
    WHERE domain = ANY(%s) AND deleted = 0
    """
    cursor.execute(sql, (domains,))
    rows = cursor.fetchall()
    cursor.close()
    conn.close()
    # 拼接后缀
    names = []
    for name, url in rows:
        _, ext = os.path.splitext(url)
        full_name = name + ext
        names.append(full_name)
    return names


def get_names_by_id(user_id):
    """
    根据用户工号获取用户有权限的文档名称。

    参数:
    job_number (str): 用户工号，例如 "D0023"。

    返回:
    list: 包含用户有权限的文档名称的列表，如果请求失败则返回空列表。
    """
    roles = get_user_roles_by_user_id(user_id)
    domain_list = []
    for data in roles:
        domain_list.append(data["name"])

    return get_names_by_domain(domain_list)


if __name__ == '__main__':
    print(get_names_by_id("D0023"))
