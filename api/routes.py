from fastapi import APIRouter

from api.controllers.admin.bi_train_data_contorller import bi_training_router
from api.controllers.admin.check_chat_history_contorller import chat_history
from api.controllers.admin.home_contorller import home_router
from api.controllers.admin.knowledge_mange_controller import upload_knowledge_router
from api.controllers.admin.text2sql_train_data_contorller import training_router

from api.controllers.dify_tools.slot_distance_controller import slot_recommendations_router
from api.controllers.dify_tools.tcx_status_controller import tcx_status_router
from api.controllers.dify_tools.feishu_controller import feishu_router

from api.controllers.services.chat_controller import chat_router
from api.controllers.services.personal_file_controller import file_router
from api.controllers.services.statistics_controller import statistics_router
from api.controllers.services.user_controller import user_router


from api.controllers.agents import meeting_minutes_controller
from api.controllers.agents.agents_router_base import agents_router

router = APIRouter()

# 注册子路由器
router.include_router(chat_router)
router.include_router(user_router)
router.include_router(file_router)
router.include_router(upload_knowledge_router)
router.include_router(training_router)
router.include_router(bi_training_router)
router.include_router(home_router)
router.include_router(chat_history)
router.include_router(tcx_status_router)
router.include_router(slot_recommendations_router)
router.include_router(statistics_router)
router.include_router(agents_router)
router.include_router(feishu_router)