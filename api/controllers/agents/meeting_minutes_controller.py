import json

from langchain_postgres import PostgresChatMessageHistory
from starlette.requests import Request
from starlette.responses import StreamingResponse

from api.controllers.agents.agents_router_base import agents_router
from api.schemas.chat import QueryRequestBasic
from api.utils.utils import get_user_roles_by_user_id, get_names_by_id
from core.agents.meeting_minutes_agent import MeetingMinutesAgent
from core.config.database_config import sync_connection
from core.config.logging import logger
from core.database.repositories import create_user_chat_map
from core.database.session import SessionLocal
from core.utlis import convert_to_openai_format

agent = MeetingMinutesAgent()


@agents_router.post("/meeting_minutes/chat")
async def meeting_minutes_chat(request_data: QueryRequestBasic, request: Request):
    query = request_data.messages.strip()
    session_id = request_data.uuid.strip()
    user_id = request_data.userId.strip()

    # 权限过滤模块
    files = get_names_by_id(user_id)
    filter_str = f"file_path in {files}"

    # 处理历史对话数据
    chat_history = PostgresChatMessageHistory(
        "chat_history",
        session_id,
        sync_connection=sync_connection
    )
    history = convert_to_openai_format(chat_history.get_messages())

    # 添加用户id和对话id到表employee_session_uuid_map中
    create_user_chat_map(SessionLocal(), user_id, session_id)
    collected_response = ""

    logger.debug(f"会议纪要助手--用户提问: {query}, 历史对话信息: {history}")

    async def generate():
        nonlocal collected_response
        async for chunk in agent.chat_stream(query, history, filter_str=filter_str):
            # 用户中断输出
            if await request.is_disconnected():
                logger.debug("客户端终止回答，停止发送")

                chat_history.add_user_message(query)
                chat_history.add_ai_message(collected_response)
                break

            decode_chunk = chunk.decode('utf-8')
            # logger.info(decode_chunk)

            if json.loads(decode_chunk[len("data: "):].strip())["type"] == "Done":
                chat_history.add_user_message(query)
                chat_history.add_ai_message(collected_response)
            else:
                collected_response += json.loads(decode_chunk[len("data: "):].strip()).get("msg", "")
                yield decode_chunk

    return StreamingResponse(generate(), media_type="text/event-stream")
