#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书电子表格内容获取脚本
支持通过app_token和sheet_id获取表格的所有内容
"""

import requests
import json
import sys
import os
from typing import Dict, List, Any, Optional, Union
import pandas as pd

# 尝试导入配置文件
try:
    import config
    HAS_CONFIG = True
except ImportError:
    HAS_CONFIG = False


class FeishuSheetsAPI:
    """飞书电子表格API客户端"""
    
    def __init__(self, app_id: str, app_secret: str):
        """
        初始化飞书API客户端
        
        Args:
            app_id: 飞书应用ID
            app_secret: 飞书应用密钥
        """
        self.app_id = app_id
        self.app_secret = app_secret
        self.access_token = None
        self.base_url = "https://open.feishu.cn/open-apis"
    
    def get_tenant_access_token(self) -> bool:
        """
        获取tenant_access_token
        
        Returns:
            bool: 是否成功获取token
        """
        url = f"{self.base_url}/auth/v3/tenant_access_token/internal"
        
        payload = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(url, json=payload, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 0:
                self.access_token = result.get("tenant_access_token")
                print(f"✅ 成功获取访问令牌")
                return True
            else:
                print(f"❌ 获取访问令牌失败: {result.get('msg', '未知错误')}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return False
    
    def get_sheet_info(self, spreadsheet_token: str) -> Optional[Dict]:
        """
        获取电子表格信息
        
        Args:
            spreadsheet_token: 电子表格token
            
        Returns:
            Dict: 表格信息，包含所有sheet的列表
        """
        if not self.access_token:
            print("❌ 请先获取访问令牌")
            return None
        
        url = f"{self.base_url}/sheets/v3/spreadsheets/{spreadsheet_token}/sheets/query"
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 0:
                return result.get("data", {})
            else:
                print(f"❌ 获取表格信息失败: {result.get('msg', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
    
    def get_sheet_range_from_info(self, sheet_info: Dict) -> str:
        """
        从sheet信息中提取数据范围
        
        Args:
            sheet_info: 单个sheet的信息字典
            
        Returns:
            str: 数据范围，如"A1:Z100"
        """
        grid_properties = sheet_info.get("grid_properties", {})
        
        if grid_properties:
            # 从grid_properties中获取行数和列数
            row_count = grid_properties.get("row_count", 1000)
            column_count = grid_properties.get("column_count", 26)
            
            # 将列数转换为字母表示
            def num_to_col_letters(num):
                letters = ''
                while num > 0:
                    num -= 1
                    letters = chr(num % 26 + ord('A')) + letters
                    num //= 26
                return letters
            
            end_col = num_to_col_letters(column_count)
            range_str = f"A1:{end_col}{row_count}"
            
            return range_str
        else:
            # 如果没有grid_properties，使用默认范围
            return "A1:Z1000"
    
    def build_range(self, sheet_id: str, start_pos: str = "A1", end_pos: str = None) -> str:
        """
        构建范围字符串
        
        Args:
            sheet_id: sheet ID
            start_pos: 开始位置，如"A1"
            end_pos: 结束位置，如"Z100"，不指定则只返回sheet_id
            
        Returns:
            str: 范围字符串，如"sheetId!A1:Z100"或"sheetId"
        """
        if not end_pos:
            if ":" in start_pos:  # 如果start_pos已经包含了范围
                return f"{sheet_id}!{start_pos}"
            return sheet_id
        
        return f"{sheet_id}!{start_pos}:{end_pos}"
    
    def get_sheet_values(self, spreadsheet_token: str, sheet_id: str, 
                        range_str: str = None) -> Optional[List[List]]:
        """
        获取指定sheet的内容
        
        Args:
            spreadsheet_token: 电子表格token
            sheet_id: sheet ID
            range_str: 范围，如"A1:Z1000"，不指定则获取所有内容
            
        Returns:
            List[List]: 表格数据，每行为一个列表
        """
        if not self.access_token:
            print("❌ 请先获取访问令牌")
            return None
        
        # 构建完整的范围标识（sheetId!range）
        # 范围格式：<sheetId>!<开始位置>:<结束位置>
        if range_str:
            complete_range = f"{sheet_id}!{range_str}"
        else:
            # 如果没有提供range_str，则使用sheet_id作为范围，获取整个工作表
            complete_range = sheet_id
        
        url = f"{self.base_url}/sheets/v2/spreadsheets/{spreadsheet_token}/values/{complete_range}"
        
        # 添加valueRenderOption和dateTimeRenderOption参数
        params = {
            # "valueRenderOption": "ToString",
            "dateTimeRenderOption": "FormattedString"
        }
        
        print(f"🔗 API请求: {url}")
        print(f"📊 渲染选项: 文本格式(ToString), 日期时间格式化(FormattedString)")
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json; charset=utf-8"
        }
        
        try:
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 0:
                values = result.get("data", {}).get("valueRange", {}).get("values", [])
                
                # 过滤掉完全空白的行
                filtered_values = []
                for row in values:
                    # 检查行是否完全为空
                    if any(cell for cell in row if str(cell).strip()):
                        filtered_values.append(row)
                    elif not filtered_values:  # 如果是开头的空行，保留一行作为可能的表头
                        filtered_values.append(row)
                
                return filtered_values
            else:
                error_msg = result.get("msg", "未知错误")
                error_code = result.get("code", -1)
                print(f"❌ 获取表格内容失败: 错误码 {error_code}, {error_msg}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
    
    def get_all_sheets_content(self, spreadsheet_token: str) -> Dict[str, List[List]]:
        """
        获取电子表格中所有sheet的内容
        
        Args:
            spreadsheet_token: 电子表格token
            
        Returns:
            Dict: 键为sheet名称，值为该sheet的数据
        """
        # 首先获取表格信息
        sheet_info = self.get_sheet_info(spreadsheet_token)
        if not sheet_info:
            return {}
        
        sheets = sheet_info.get("sheets", [])
        if not sheets:
            print("❌ 未找到任何sheet")
            return {}
        
        print(f"📊 发现 {len(sheets)} 个sheet")
        
        all_content = {}
        
        for sheet in sheets:
            sheet_id = sheet.get("sheet_id")
            sheet_title = sheet.get("title", f"Sheet_{sheet_id}")
            
            print(f"📖 正在读取: {sheet_title}")
            
            # 从sheet信息中获取范围
            cell_range = self.get_sheet_range_from_info(sheet)
            print(f"📐 使用范围: {cell_range}")
            
            values = self.get_sheet_values(spreadsheet_token, sheet_id, cell_range)
            if values is not None:
                all_content[sheet_title] = values
                print(f"✅ 成功读取 {sheet_title}，共 {len(values)} 行数据")
            else:
                print(f"❌ 读取 {sheet_title} 失败")
        
        return all_content
    
    def save_to_json(self, data: Dict, filename: str = "feishu_sheets_data.json"):
        """
        将数据保存为JSON文件
        
        Args:
            data: 要保存的数据，键为sheet名称，值为字典列表
            filename: 文件名
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 数据已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
    
    def print_sheet_summary(self, data: Dict[str, List]):
        """
        打印表格内容摘要
        
        Args:
            data: 表格数据，可以是二维数组或字典列表
        """
        print("\n" + "="*50)
        print("📋 表格内容摘要")
        print("="*50)
        
        for sheet_name, values in data.items():
            print(f"\n📄 Sheet: {sheet_name}")
            print(f"   行数: {len(values)}")
            
            if values:
                # 检查数据格式（二维数组或字典列表）
                if isinstance(values[0], dict):
                    # 字典列表格式
                    if len(values) > 0:
                        # 显示列数（以第一行为准）
                        col_count = len(values[0].keys())
                        print(f"   列数: {col_count}")
                        
                        # 显示前几行数据作为预览
                        print("   预览:")
                        for i, row in enumerate(values[:3]):  # 只显示前3行
                            # 取前5个键值对
                            preview_items = list(row.items())[:5]
                            row_preview = [f"{k}: {str(v)[:20] + '...' if len(str(v)) > 20 else v}" for k, v in preview_items]
                            print(f"     第{i+1}行: {row_preview}")
                else:
                    # 二维数组格式
                    # 显示列数（以第一行为准）
                    col_count = len(values[0]) if values[0] else 0
                    print(f"   列数: {col_count}")
                    
                    # 显示前几行数据作为预览
                    print("   预览:")
                    for i, row in enumerate(values[:3]):  # 只显示前3行
                        row_preview = [str(cell)[:20] + "..." if len(str(cell)) > 20 else str(cell) for cell in row[:5]]  # 只显示前5列
                        print(f"     第{i+1}行: {row_preview}")
                
                if len(values) > 3:
                    print(f"     ... (还有 {len(values)-3} 行)")

    def expand_merged_cells(self, values: List[List], merged_cells: List[Dict]) -> List[List]:
        """
        展开合并单元格，将合并单元格的值填充到范围内的所有单元格
        
        Args:
            values: 表格数据
            merged_cells: 合并单元格信息列表
            
        Returns:
            List[List]: 处理后的表格数据
        """
        if not merged_cells:
            print("🔍 没有发现合并单元格信息，跳过处理")
            return values
        
        print(f"🔍 开始处理 {len(merged_cells)} 个合并单元格")
        print(f"🔍 原始数据维度: {len(values)} 行 x {len(values[0]) if values else 0} 列")
        
        # 创建数据的深拷贝，避免修改原始数据
        result = [row[:] for row in values]
        
        # 处理每一个合并单元格区域
        for i, merge_info in enumerate(merged_cells):
            start_row = merge_info.get("start_row_index", 0)
            end_row = merge_info.get("end_row_index", 0)
            start_col = merge_info.get("start_column_index", 0)
            end_col = merge_info.get("end_column_index", 0)
            
            print(f"🔍 合并单元格 #{i+1}: 范围 [{start_row},{start_col}] 到 [{end_row-1},{end_col-1}]")
            
            # 获取合并单元格左上角的值
            if start_row < len(values) and start_col < len(values[start_row]):
                value = values[start_row][start_col]
                print(f"🔍 合并单元格值: {value}")
                
                # 将值填充到合并区域的所有单元格
                filled_cells = 0
                for r in range(start_row, end_row+1):
                    if r >= len(result):
                        print(f"⚠️ 行索引 {r} 超出范围，跳过")
                        continue
                    for c in range(start_col, end_col+1):
                        if c >= len(result[r]):
                            print(f"⚠️ 列索引 {c} 在行 {r} 中超出范围，跳过")
                            continue
                        # 仅当单元格不等于左上角值时才打印
                        if result[r][c] != value:
                            print(f"🔄 填充单元格 [{r},{c}]: {result[r][c]} → {value}")
                        result[r][c] = value
                        filled_cells += 1
                
                print(f"✅ 已填充 {filled_cells} 个单元格")
            else:
                print(f"⚠️ 合并单元格左上角坐标 [{start_row},{start_col}] 超出数据范围，跳过")
        
        print(f"✅ 合并单元格处理完成，共处理 {len(merged_cells)} 个区域")
        return result
    
    def clean_data_to_dataframe(self, values: List[List], sheet_name: str = None) -> tuple:
        """
        将表格数据转换为DataFrame并清理空行空列
        
        Args:
            values: 表格数据
            sheet_name: sheet名称，用于添加来源列
            
        Returns:
            tuple: (清理后的DataFrame, 删除的序号行数量, 第一行是否被跳过)
        """
        if not values:
            return pd.DataFrame(), 0, False
        
        # 检查第一行是否所有值都相同
        first_row_skipped = False
        seq_rows_removed = 0  # 初始化seq_rows_removed
        if len(values) > 1 and len(set([str(x) for x in values[0] if x is not None])) <= 1:
            first_row_skipped = True
            print("⚠️ 检测到第一行所有值相同，使用第二行作为表头")
        
        # 确保每行长度一致
        max_cols = max(len(row) for row in values)
        padded_values = [row + [None] * (max_cols - len(row)) for row in values]
        
        # 根据第一行是否被跳过创建DataFrame
        if first_row_skipped:
            if len(padded_values) > 2:
                # 使用第二行作为表头，从第三行开始作为数据
                df = pd.DataFrame(padded_values[2:], columns=padded_values[1])
            else:
                # 如果只有两行，使用第二行作为表头，没有数据行
                df = pd.DataFrame(columns=padded_values[1])
        else:
            # 正常情况：使用第一行作为表头
            if len(padded_values) > 1:
                df = pd.DataFrame(padded_values[1:], columns=padded_values[0])
            else:
                df = pd.DataFrame(padded_values)
        
        # 删除全空的行
        df = df.dropna(how='all')
        
        # 删除全空的列
        df = df.dropna(axis=1, how='all')
        
        # 异常处理
        try:
            # 将剩余的null值替换为空白字符串
            df = df.fillna('')
        except Exception as e:
            print(f"❌ 处理null值时出错: {e}")
            # 如果出错，尝试一种更安全的方式替换null值
            try:
                for col in df.columns:
                    df[col] = df[col].fillna('')
            except Exception as e2:
                print(f"❌ 二次处理null值失败: {e2}")
        
        # 处理异常情况：删除只有第一列有序号的行
        seq_rows_removed = 0
        if not df.empty and len(df.columns) >= 3:
            # 收集可能的序号行
            sequence_rows = []
            
            for idx, row in df.iterrows():
                # 检查是否只有第一列有值
                non_null_count = row.count()
                first_col_value = str(row.iloc[0]) if not pd.isna(row.iloc[0]) else ""
                
                # 如果只有第一列有值，并且该值是数字序号
                if non_null_count == 1 and first_col_value.strip() and first_col_value.replace('.', '', 1).isdigit():
                    # 提取纯数字部分
                    num_value = float(first_col_value.replace(',', '').strip())
                    sequence_rows.append((idx, num_value))
            
            # 检查序号是否递增
            rows_to_delete = []
            if len(sequence_rows) >= 2:  # 至少需要两行才能判断递增
                # 按序号排序
                sequence_rows.sort(key=lambda x: x[1])
                
                # 检查是否构成等差数列（简化判断：检查是否大致递增）
                is_increasing = True
                for i in range(1, len(sequence_rows)):
                    if sequence_rows[i][1] <= sequence_rows[i-1][1]:
                        is_increasing = False
                        break
                
                # 如果是递增的序号行，则删除
                if is_increasing:
                    rows_to_delete = [row[0] for row in sequence_rows]
                    print(f"🔍 检测到{len(rows_to_delete)}行递增序号行")
            
            # 如果有要删除的行
            if rows_to_delete:
                # 删除这些行
                df = df.drop(rows_to_delete)
                seq_rows_removed = len(rows_to_delete)
                print(f"⚠️ 删除了 {seq_rows_removed} 行只有递增序号的异常行")
                
                # 记录删除的序号
                if seq_rows_removed > 0 and sequence_rows:
                    seq_values = [f"{row[1]}" for row in sequence_rows[:5]]
                    if len(sequence_rows) > 5:
                        seq_values.append("...")
                    print(f"   序号值: {', '.join(seq_values)}")
        
        # 添加表示sheet名称的列
        if sheet_name and not df.empty:
            # 选择一个不会与现有列冲突的列名
            sheet_col_name = "所属表格"
            if sheet_col_name in df.columns:
                i = 1
                while f"{sheet_col_name}_{i}" in df.columns:
                    i += 1
                sheet_col_name = f"{sheet_col_name}_{i}"
            
            # 添加列，值为sheet名称
            df[sheet_col_name] = sheet_name
            print(f"✅ 已添加sheet名称列: {sheet_col_name} = {sheet_name}")
        
        return df, seq_rows_removed, first_row_skipped
    
    def save_to_excel(self, data: Dict[str, pd.DataFrame], filename: str = "feishu_sheets_data.xlsx"):
        """
        将数据保存为Excel文件
        
        Args:
            data: 要保存的数据，键为sheet名称，值为DataFrame
            filename: 文件名
        """
        try:
            with pd.ExcelWriter(filename) as writer:
                for sheet_name, df in data.items():
                    # 确保sheet名称有效
                    valid_sheet_name = sheet_name[:31]  # Excel sheet名称最长31个字符
                    df.to_excel(writer, sheet_name=valid_sheet_name, index=False)
            print(f"📊 数据已保存到Excel: {filename}")
        except Exception as e:
            print(f"❌ 保存Excel文件失败: {e}")
    
    def process_sheet_data(self, spreadsheet_token: str) -> Dict[str, Any]:
        """
        获取电子表格的所有sheet内容，处理合并单元格，并清理数据
        
        Args:
            spreadsheet_token: 电子表格token
            
        Returns:
            Dict: 包含原始数据、DataFrame和清理后数据的字典
        """
        # 获取表格信息
        sheet_info = self.get_sheet_info(spreadsheet_token)
        if not sheet_info:
            return {}
        
        sheets = sheet_info.get("sheets", [])
        if not sheets:
            print("❌ 未找到任何sheet")
            return {}
        
        print(f"📊 发现 {len(sheets)} 个sheet")
        
        # 存储结果的字典
        result = {
            "raw_data": {},      # 原始数据
            "dataframes": {},    # 转换为DataFrame的数据
            "cleaned_data": {},  # 清理后的数据（可转为JSON）
            "merged_cells": {},  # 合并单元格信息
            "seq_rows_removed": {},  # 每个sheet删除的序号行数量
            "first_row_skipped": {}  # 每个sheet是否跳过了第一行
        }
        
        for sheet in sheets:
            sheet_id = sheet.get("sheet_id")
            sheet_title = sheet.get("title", f"Sheet_{sheet_id}")
            
            print(f"📖 正在处理: {sheet_title}")
            
            # 获取合并单元格信息
            merged_cells = sheet.get("merges", [])
            if merged_cells:
                print(f"🔄 发现 {len(merged_cells)} 个合并单元格")
                result["merged_cells"][sheet_title] = merged_cells
            
            # 从sheet信息中获取范围
            cell_range = self.get_sheet_range_from_info(sheet)
            print(f"📐 使用范围: {cell_range}")
            
            # 获取原始数据
            values = self.get_sheet_values(spreadsheet_token, sheet_id, cell_range)
            if values is None:
                print(f"❌ 读取 {sheet_title} 失败")
                continue
            
            result["raw_data"][sheet_title] = values
            print(f"✅ 成功读取 {sheet_title}，共 {len(values)} 行数据")
            
            # 展开合并单元格
            if merged_cells:
                expanded_values = self.expand_merged_cells(values, merged_cells)
                print(f"✅ 已展开合并单元格")
            else:
                expanded_values = values
            
            # 转换为DataFrame并清理
            df, seq_rows_removed, first_row_skipped = self.clean_data_to_dataframe(expanded_values, sheet_title)
            result["dataframes"][sheet_title] = df
            result["seq_rows_removed"][sheet_title] = seq_rows_removed
            result["first_row_skipped"][sheet_title] = first_row_skipped
            
            # 将DataFrame转换为JSON格式（使用key-value结构）
            if not df.empty:
                # 将DataFrame转换为字典列表，每行变成一个字典，列名作为键
                cleaned_data = df.to_dict(orient='records')
                print(f"✅ 数据清理完成，保留 {df.shape[0]} 行，{df.shape[1]} 列")
                print(f"🔄 数据格式：已转换为键值对JSON结构")
                
                # 检查原始数据第一行值是否都相同
                if len(values) > 1:
                    first_row_values = [str(x) for x in values[0] if x is not None and str(x).strip()]
                    if first_row_values and len(set(first_row_values)) <= 1:
                        print(f"ℹ️ 原始数据第一行 \"{values[0][0]}\" 被舍弃，使用第二行作为表头")
            else:
                cleaned_data = []
                print("⚠️ 清理后数据为空")
            
            result["cleaned_data"][sheet_title] = cleaned_data
        
        return result

    def print_processing_details(self, sheet_title: str, values: List[List], 
                             merged_cells_count: int, 
                             original_rows: int, cleaned_rows: int, 
                             original_cols: int, cleaned_cols: int,
                             first_row_skipped: bool = False,
                             seq_rows_removed: int = 0):
        """
        打印处理详情
        
        Args:
            sheet_title: 表格名称
            values: 原始数据
            merged_cells_count: 合并单元格数量
            original_rows: 原始行数
            cleaned_rows: 清理后行数
            original_cols: 原始列数
            cleaned_cols: 清理后列数
            first_row_skipped: 是否跳过了第一行
            seq_rows_removed: 删除的序号行数量
        """
        print("\n" + "-"*50)
        print(f"📊 [{sheet_title}] 处理详情:")
        print("-"*50)
        
        # 数据维度变化
        print(f"🔢 数据维度: {original_rows}行 x {original_cols}列 → {cleaned_rows}行 x {cleaned_cols}列")
        
        # 合并单元格
        if merged_cells_count > 0:
            print(f"🔄 合并单元格: {merged_cells_count}个 (已全部展开)")
        
        # 表头处理
        if first_row_skipped and len(values) > 0:
            print(f"📑 表头处理: 检测到第一行值相同 \"{values[0][0]}\", 已使用第二行作为表头")
        
        # 数据清洗
        removed_rows = original_rows - cleaned_rows
        removed_cols = original_cols - cleaned_cols
        
        if removed_rows > 0:
            print(f"🧹 数据清洗: 删除了{removed_rows}行空行")
        
        if removed_cols > 0:
            print(f"🧹 数据清洗: 删除了{removed_cols}列空列")
            
        # 序号行处理
        if seq_rows_removed > 0:
            print(f"🔄 数据异常处理: 删除了{seq_rows_removed}行只有递增序号的行")
        
        # 数据预览
        if cleaned_rows > 0:
            print(f"👁️ 数据预览: 前3行 x 前5列")
            try:
                preview_data = values[:min(4, len(values))]  # 获取前4行(包括表头)
                for i, row in enumerate(preview_data):
                    row_preview = [str(cell)[:20] + "..." if len(str(cell)) > 20 else str(cell) for cell in row[:min(5, len(row))]]
                    print(f"   {'表头' if i == 0 else f'第{i}行'}: {row_preview}")
            except Exception as e:
                print(f"   预览失败: {e}")
        
        print("-"*50)

    def save_to_jsonl(self, data: Dict[str, List[Dict]], base_filename: str = "feishu_sheets_data"):
        """
        将数据保存为JSONL文件（每行一个JSON记录），按表格拆分为多个文件
        
        Args:
            data: 要保存的数据，键为sheet名称，值为字典列表
            base_filename: 文件名前缀
        """
        saved_files = []
        
        for sheet_name, records in data.items():
            if not records:
                continue
                
            # 创建安全的文件名（替换非法字符）
            safe_sheet_name = "".join(c if c.isalnum() or c in ['-', '_'] else '_' for c in sheet_name)
            filename = f"{base_filename}_{safe_sheet_name}.txt"
            
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    for record in records:
                        # 每行写入一个JSON记录
                        f.write(json.dumps(record, ensure_ascii=False) + '\n')
                saved_files.append(filename)
                print(f"💾 {sheet_name} 数据已保存到JSONL: {filename} ({len(records)}条记录)")
            except Exception as e:
                print(f"❌ 保存{sheet_name}的JSONL文件失败: {e}")
        
        if saved_files:
            print(f"✅ 共保存了 {len(saved_files)} 个JSONL文件")
        else:
            print("⚠️ 没有保存任何JSONL文件")

    def get_media_tmp_download_url(self, file_tokens: Union[List[str], Dict[str, str]], extra: str = None) -> Dict:
        """
        获取素材临时下载链接
        
        Args:
            file_tokens: 素材文件的token列表或字典，
                         如果是列表，每个元素为文件token；
                         如果是字典，键为文件名，值为文件token
            extra: 拓展信息，用于鉴权
            
        Returns:
            Dict: 成功时返回包含临时下载链接的字典，键为文件名，值为下载链接。
                 失败时返回包含错误信息的字典，格式为 {"error": "错误信息"}
        """
        if not self.access_token:
            error_msg = "请先获取访问令牌"
            print(f"❌ {error_msg}")
            return {"error": error_msg}
        
        if not file_tokens:
            error_msg = "文件token列表不能为空"
            print(f"❌ {error_msg}")
            return {"error": error_msg}
        
        # 判断输入类型是列表还是字典
        is_dict_input = isinstance(file_tokens, dict)
        
        # 处理字典输入
        if is_dict_input:
            filename_token_map = file_tokens
            # 提取所有token值作为列表
            token_list = list(filename_token_map.values())
        else:
            # 如果是列表输入，直接使用
            token_list = file_tokens
            # 创建token到文件名的映射，对于列表输入，文件名就是token本身
            filename_token_map = {token: token for token in token_list}
        
        # 一次最多支持5个文件token
        if len(token_list) > 5:
            print(f"⚠️ 一次最多支持5个文件token，当前提供了{len(token_list)}个，将只处理前5个")
            token_list = token_list[:5]
            if is_dict_input:
                # 如果是字典输入，需要重建字典
                temp_dict = {}
                for i, (filename, token) in enumerate(filename_token_map.items()):
                    if i >= 5:
                        break
                    temp_dict[filename] = token
                filename_token_map = temp_dict
            else:
                # 如果是列表输入，更新token到文件名的映射
                filename_token_map = {token: token for token in token_list}
        
        url = f"{self.base_url}/drive/v1/medias/batch_get_tmp_download_url"
        
        # 构建查询参数
        params = []
        for token in token_list:
            params.append(("file_tokens", token))
        
        if extra:
            params.append(("extra", extra))
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json; charset=utf-8"
        }
        
        try:
            print(f"🔗 API请求: {url}")
            print(f"🔑 请求文件tokens: {', '.join(token_list)}")
            
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 0:
                tmp_download_urls = result.get("data", {}).get("tmp_download_urls", [])
                
                # 为token创建反向映射（token -> 文件名）
                token_to_filename = {token: filename for filename, token in filename_token_map.items()}
                
                # 构建结果：文件名 -> 下载链接
                download_urls = {}
                for item in tmp_download_urls:
                    file_token = item.get("file_token")
                    download_url = item.get("tmp_download_url")
                    if file_token and download_url and file_token in token_to_filename:
                        filename = token_to_filename[file_token]
                        download_urls[filename] = download_url
                        print(f"✅ 成功获取文件 {filename} 的临时下载链接")
                
                if not download_urls:
                    error_msg = "未获取到任何临时下载链接"
                    print(f"⚠️ {error_msg}")
                    return {"error": error_msg}
                    
                return download_urls
            else:
                error_msg = result.get("msg", "未知错误")
                error_code = result.get("code", -1)
                full_error_msg = f"获取临时下载链接失败: 错误码 {error_code}, {error_msg}"
                print(f"❌ {full_error_msg}")
                return {"error": full_error_msg}
                
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败: {e}"
            print(f"❌ {error_msg}")
            return {"error": error_msg}
    
    def download_file(self, file_token: Union[str, Dict[str, str]], save_path: str, extra: str = None) -> bool:
        """
        下载文件到指定路径
        
        Args:
            file_token: 文件token或字典，
                      如果是字符串，直接作为文件token使用；
                      如果是字典，格式为{"文件名": "token"}，将使用字典中的第一个token
            save_path: 保存路径
            extra: 拓展信息，用于鉴权
            
        Returns:
            bool: 是否成功下载
        """
        # 获取临时下载链接
        if isinstance(file_token, dict):
            # 如果是字典，直接传给get_media_tmp_download_url
            download_urls = self.get_media_tmp_download_url(file_token, extra)
            
            # 检查是否有错误
            if "error" in download_urls:
                print(f"❌ 获取下载链接失败: {download_urls['error']}")
                return False
            
            # 获取第一个文件名和下载链接
            filename = next(iter(download_urls.keys()))
            download_url = download_urls[filename]
            
            print(f"📁 准备下载文件: {filename}")
        else:
            # 如果是字符串，包装成列表
            download_urls = self.get_media_tmp_download_url([file_token], extra)
            
            # 检查是否有错误
            if "error" in download_urls:
                print(f"❌ 获取下载链接失败: {download_urls['error']}")
                return False
            
            if file_token not in download_urls:
                print(f"❌ 未找到文件 {file_token} 的下载链接")
                return False
            
            download_url = download_urls[file_token]
        
        try:
            print(f"⬇️ 正在下载文件到: {save_path}")
            
            # 下载文件
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            # 确保目录存在
            os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)
            
            # 写入文件
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"✅ 文件已成功下载到: {save_path}")
            return True
            
        except Exception as e:
            print(f"❌ 下载文件失败: {e}")
            return False


def main():
    """主函数"""
    print("🚀 飞书电子表格内容获取工具")
    print("="*50)
    
    # 从配置文件获取默认设置
    default_app_id = config.APP_ID if HAS_CONFIG and hasattr(config, 'APP_ID') else ""
    default_app_secret = config.APP_SECRET if HAS_CONFIG and hasattr(config, 'APP_SECRET') else ""
    default_token = config.DEFAULT_SPREADSHEET_TOKEN if HAS_CONFIG and hasattr(config, 'DEFAULT_SPREADSHEET_TOKEN') else ""
    default_filename = config.DEFAULT_OUTPUT_FILENAME if HAS_CONFIG and hasattr(config, 'DEFAULT_OUTPUT_FILENAME') else "feishu_data"
    always_save = (HAS_CONFIG and hasattr(config, 'ALWAYS_SAVE_JSON') and config.ALWAYS_SAVE_JSON and
                   hasattr(config, 'ALWAYS_SAVE_EXCEL') and config.ALWAYS_SAVE_EXCEL)

    # 获取用户输入或使用默认值
    prompt_app_id = f"请输入飞书应用ID (App ID) [默认: {default_app_id if default_app_id else '无'}]: "
    app_id = input(prompt_app_id).strip() or default_app_id
    if not app_id:
        print("❌ App ID不能为空")
        return

    prompt_secret = f"请输入飞书应用密钥 (App Secret) [默认: {'已设置' if default_app_secret else '无'}]: "
    app_secret = input(prompt_secret).strip() or default_app_secret
    if not app_secret:
        print("❌ App Secret不能为空")
        return

    prompt_token = f"请输入电子表格Token [默认: {default_token if default_token else '无'}]: "
    spreadsheet_token = input(prompt_token).strip() or default_token
    if not spreadsheet_token:
        print("❌ 电子表格Token不能为空")
        return

    # 可选：指定特定的sheet_id
    sheet_id = input("请输入Sheet ID (留空获取所有sheet): ").strip()
    
    # 初始化API客户端
    api = FeishuSheetsAPI(app_id='cli_a74bac43fcfd900b', app_secret='ummD1SpPIvZskDlpmMUtIboLyEJxLO7V')
    
    # 获取访问令牌
    if not api.get_tenant_access_token():
        return
    
    try:
        if sheet_id:
            # 获取指定sheet的内容
            print(f"\n📖 正在获取指定Sheet内容...")
            
            # 首先获取表格信息以确定范围
            sheet_info_data = api.get_sheet_info(spreadsheet_token)
            range_str = None
            sheet_title = f"Sheet_{sheet_id}"
            merged_cells = []
            
            if sheet_info_data:
                sheets = sheet_info_data.get("sheets", [])
                # 查找指定的sheet
                target_sheet = None
                for sheet in sheets:
                    if sheet.get("sheet_id") == sheet_id:
                        target_sheet = sheet
                        sheet_title = sheet.get("title", sheet_title)
                        merged_cells = sheet.get("merges", [])
                        break
                
                if target_sheet:
                    range_str = api.get_sheet_range_from_info(target_sheet)
                    print(f"📐 使用范围: {range_str}")
                    print(f"📋 完整范围: {sheet_id}!{range_str}")
                    if merged_cells:
                        print(f"🔄 发现 {len(merged_cells)} 个合并单元格")
                else:
                    print(f"⚠️ 未找到指定的Sheet ID: {sheet_id}")
            
            values = api.get_sheet_values(spreadsheet_token, sheet_id, range_str)
            
            if values:
                # 处理合并单元格
                if merged_cells:
                    expanded_values = api.expand_merged_cells(values, merged_cells)
                    print(f"✅ 已展开合并单元格")
                else:
                    expanded_values = values
                
                # 转换为DataFrame并清理
                df, seq_rows_removed, first_row_skipped = api.clean_data_to_dataframe(expanded_values, sheet_title)
                
                # 将DataFrame转换为JSON格式（使用key-value结构）
                if not df.empty:
                    # 将DataFrame转换为字典列表，每行变成一个字典，列名作为键
                    cleaned_data = df.to_dict(orient='records')
                    print(f"✅ 数据清理完成，保留 {df.shape[0]} 行，{df.shape[1]} 列")
                    print(f"🔄 数据格式：已转换为键值对JSON结构")
                    
                    # 检查原始数据第一行值是否都相同
                    if len(values) > 1:
                        first_row_values = [str(x) for x in values[0] if x is not None and str(x).strip()]
                        if first_row_values and len(set(first_row_values)) <= 1:
                            print(f"ℹ️ 原始数据第一行 \"{values[0][0]}\" 被舍弃，使用第二行作为表头")
                else:
                    cleaned_data = []
                    print("⚠️ 清理后数据为空")
                
                # 构建数据字典
                raw_data = {sheet_title: values}
                dataframes = {sheet_title: df}
                cleaned_json = {sheet_title: cleaned_data}
                
                # 打印数据摘要（使用原始数据）
                api.print_sheet_summary(raw_data)
                
                # 打印处理详情
                api.print_processing_details(
                    sheet_title, 
                    values, 
                    len(merged_cells), 
                    len(values), 
                    df.shape[0], 
                    len(values[0]) if values else 0, 
                    df.shape[1], 
                    first_row_skipped,
                    seq_rows_removed
                )
                
                # 询问是否保存或自动保存
                save = "y" if always_save else input("\n💾 是否保存数据? (y/n): ").strip().lower()
                if save in ['y', 'yes', '是']:
                    base_filename = input(f"请输入文件名前缀 [默认: {default_filename}]: ").strip() or default_filename
                    
                    # 保存为JSON
                    json_filename = f"{base_filename}.json"
                    api.save_to_json(cleaned_json, json_filename)
                    
                    # 保存为Excel
                    excel_filename = f"{base_filename}.xlsx"
                    api.save_to_excel(dataframes, excel_filename)
                    
                    # 保存为JSONL（每个表格一个文件）
                    api.save_to_jsonl(cleaned_json, base_filename)
            else:
                print("❌ 未能获取到数据")
        else:
            # 获取并处理所有sheet的内容
            print(f"\n📖 正在获取和处理所有Sheet内容...")
            print(f"🔄 将自动处理所有sheet的合并单元格并清理数据")
            processed_data = api.process_sheet_data(spreadsheet_token)
            
            if processed_data and processed_data.get("raw_data"):
                # 打印原始数据摘要
                api.print_sheet_summary(processed_data["raw_data"])
                
                # 显示合并单元格处理汇总
                merged_cells = processed_data.get("merged_cells", {})
                total_merged_cells = sum(len(cells) for cells in merged_cells.values())
                if total_merged_cells > 0:
                    print(f"\n🔄 已处理 {total_merged_cells} 个合并单元格，跨 {len(merged_cells)} 个sheet")
                
                # 显示数据清洗汇总
                raw_rows_total = sum(len(data) for data in processed_data["raw_data"].values())
                clean_rows_total = sum(df.shape[0] for df in processed_data["dataframes"].values())
                print(f"✂️ 数据清洗: 原始数据 {raw_rows_total} 行 → 清洗后 {clean_rows_total} 行")
                
                # 打印处理详情
                for sheet_title, values in processed_data["raw_data"].items():
                    df = processed_data["dataframes"][sheet_title]
                    merged_cells_list = processed_data["merged_cells"].get(sheet_title, [])
                    
                    # 使用process_sheet_data返回的值
                    seq_rows_removed = processed_data.get("seq_rows_removed", {}).get(sheet_title, 0)
                    first_row_skipped = processed_data.get("first_row_skipped", {}).get(sheet_title, False)
                    
                    api.print_processing_details(
                        sheet_title, 
                        values, 
                        len(merged_cells_list), 
                        len(values), 
                        df.shape[0], 
                        len(values[0]) if values else 0,
                        df.shape[1], 
                        first_row_skipped,
                        seq_rows_removed
                    )
                
                # 询问是否保存或自动保存
                save = "y" if always_save else input("\n💾 是否保存数据? (y/n): ").strip().lower()
                if save in ['y', 'yes', '是']:
                    base_filename = input(f"请输入文件名前缀 [默认: {default_filename}]: ").strip() or default_filename
                    
                    # 保存为JSON
                    json_filename = f"{base_filename}.json"
                    api.save_to_json(processed_data["cleaned_data"], json_filename)
                    
                    # 保存为Excel
                    excel_filename = f"{base_filename}.xlsx"
                    api.save_to_excel(processed_data["dataframes"], excel_filename)
                    
                    # 保存为JSONL（每个表格一个文件）
                    api.save_to_jsonl(processed_data["cleaned_data"], base_filename)
            else:
                print("❌ 未能获取到任何数据")
        
        print("\n" + "="*50)
        print("✅ 处理完成！")
        print("="*50)
    
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        import traceback
        print(traceback.format_exc())


if __name__ == "__main__":
    # main()
    
    # 初始化API客户端
    api = FeishuSheetsAPI(app_id='cli_a74bac43fcfd900b', app_secret='ummD1SpPIvZskDlpmMUtIboLyEJxLO7V')
    spreadsheet_token = "V7orsnuzkhn9pjtokwDcv7uHnQh"
    # 获取访问令牌
    if not api.get_tenant_access_token():
        print("❌ 获取访问令牌失败")
        exit()

        # 首先获取表格信息以确定范围
        sheet_info_data = api.get_sheet_info(spreadsheet_token)
        range_str = None
        sheet_title = f"Sheet_{sheet_id}"
        merged_cells = []

        if sheet_info_data:
            sheets = sheet_info_data.get("sheets", [])
            # 查找指定的sheet
            target_sheet = None
            for sheet in sheets:
                if sheet.get("sheet_id") == sheet_id:
                    target_sheet = sheet
                    sheet_title = sheet.get("title", sheet_title)
                    merged_cells = sheet.get("merges", [])
                    break

            if target_sheet:
                range_str = api.get_sheet_range_from_info(target_sheet)
                print(f"📐 使用范围: {range_str}")
                print(f"📋 完整范围: {sheet_id}!{range_str}")
                if merged_cells:
                    print(f"🔄 发现 {len(merged_cells)} 个合并单元格")
            else:
                print(f"⚠️ 未找到指定的Sheet ID: {sheet_id}")

        values = api.get_sheet_values(spreadsheet_token, sheet_id, range_str)

        if values:
            # 处理合并单元格
            if merged_cells:
                expanded_values = api.expand_merged_cells(values, merged_cells)
                print(f"✅ 已展开合并单元格")
            else:
                expanded_values = values

            # 转换为DataFrame并清理
            df, seq_rows_removed, first_row_skipped = api.clean_data_to_dataframe(expanded_values, sheet_title)

            # 将DataFrame转换为JSON格式（使用key-value结构）
            if not df.empty:
                # 将DataFrame转换为字典列表，每行变成一个字典，列名作为键
                cleaned_data = df.to_dict(orient='records')
                print(f"✅ 数据清理完成，保留 {df.shape[0]} 行，{df.shape[1]} 列")
                print(f"🔄 数据格式：已转换为键值对JSON结构")

                # 检查原始数据第一行值是否都相同
                if len(values) > 1:
                    first_row_values = [str(x) for x in values[0] if x is not None and str(x).strip()]
                    if first_row_values and len(set(first_row_values)) <= 1:
                        print(f"ℹ️ 原始数据第一行 \"{values[0][0]}\" 被舍弃，使用第二行作为表头")
            else:
                cleaned_data = []
                print("⚠️ 清理后数据为空")

            # 构建数据字典
            raw_data = {sheet_title: values}
            dataframes = {sheet_title: df}
            cleaned_json = {sheet_title: cleaned_data}

            # 打印数据摘要（使用原始数据）
            api.print_sheet_summary(raw_data)

            # 打印处理详情
            api.print_processing_details(
                sheet_title,
                values,
                len(merged_cells),
                len(values),
                df.shape[0],
                len(values[0]) if values else 0,
                df.shape[1],
                first_row_skipped,
                seq_rows_removed
            )

            # 询问是否保存或自动保存
            save = "y" if always_save else input("\n💾 是否保存数据? (y/n): ").strip().lower()
            if save in ['y', 'yes', '是']:
                base_filename = input(f"请输入文件名前缀 [默认: {default_filename}]: ").strip() or default_filename

                # 保存为JSON
                json_filename = f"{base_filename}.json"
                api.save_to_json(cleaned_json, json_filename)

                # 保存为Excel
                excel_filename = f"{base_filename}.xlsx"
                api.save_to_excel(dataframes, excel_filename)

                # 保存为JSONL（每个表格一个文件）
                api.save_to_jsonl(cleaned_json, base_filename)
        else:
            print("❌ 未能获取到数据")
    else:
        # 获取并处理所有sheet的内容
        print(f"\n📖 正在获取和处理所有Sheet内容...")
        print(f"🔄 将自动处理所有sheet的合并单元格并清理数据")
        processed_data = api.process_sheet_data(spreadsheet_token)

        if processed_data and processed_data.get("raw_data"):
            # 打印原始数据摘要
            api.print_sheet_summary(processed_data["raw_data"])

            # 显示合并单元格处理汇总
            merged_cells = processed_data.get("merged_cells", {})
            total_merged_cells = sum(len(cells) for cells in merged_cells.values())
            if total_merged_cells > 0:
                print(f"\n🔄 已处理 {total_merged_cells} 个合并单元格，跨 {len(merged_cells)} 个sheet")

            # 显示数据清洗汇总
            raw_rows_total = sum(len(data) for data in processed_data["raw_data"].values())
            clean_rows_total = sum(df.shape[0] for df in processed_data["dataframes"].values())
            print(f"✂️ 数据清洗: 原始数据 {raw_rows_total} 行 → 清洗后 {clean_rows_total} 行")

            with open('../../../test/sample.json', 'w', encoding='utf-8') as f:
                json.dump(processed_data["cleaned_data"]["Sheet1"], f, ensure_ascii=False, indent=4)

            # 打印处理详情
            for sheet_title, values in processed_data["raw_data"].items():
                df = processed_data["dataframes"][sheet_title]
                merged_cells_list = processed_data["merged_cells"].get(sheet_title, [])

                # 使用process_sheet_data返回的值
                seq_rows_removed = processed_data.get("seq_rows_removed", {}).get(sheet_title, 0)
                first_row_skipped = processed_data.get("first_row_skipped", {}).get(sheet_title, False)

                api.print_processing_details(
                    sheet_title,
                    values,
                    len(merged_cells_list),
                    len(values),
                    df.shape[0],
                    len(values[0]) if values else 0,
                    df.shape[1],
                    first_row_skipped,
                    seq_rows_removed
                )

        else:
            print("❌ 未能获取到任何数据")

    print("\n" + "=" * 50)
    print("✅ 处理完成！")
    print("=" * 50)
