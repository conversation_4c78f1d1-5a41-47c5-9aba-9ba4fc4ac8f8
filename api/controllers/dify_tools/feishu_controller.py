from fastapi import APIRouter, Query, HTTPException
import requests
import json

from api.controllers.dify_tools.feishu_sheets import FeishuSheetsAPI

feishu_router = APIRouter(prefix="/dify_tools", tags=["飞书工具"])

api = FeishuSheetsAPI(app_id='cli_a74bac43fcfd900b', app_secret='ummD1SpPIvZskDlpmMUtIboLyEJxLO7V')


@feishu_router.get("/get_sheet_data")
def get_sheet_data(spreadsheet_token: str):
    for i in range(3):
        if api.get_tenant_access_token():
            processed_data = api.process_sheet_data(spreadsheet_token)

            return processed_data["cleaned_data"]["Sheet1"]
        else:
            print("❌ 获取访问令牌失败，重试")
            import time
            time.sleep(3)
