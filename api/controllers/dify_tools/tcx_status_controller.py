from fastapi import APIRouter, Query, HTTPException
import requests
import json

tcx_status_router = APIRouter(prefix="/dify_tools", tags=["机台状态"])


@tcx_status_router.get("/tcx_status")
def get_machine_data(device_no: str = Query(..., description="机台号，例如 FX01")):
    # 登录获取 token
    login_url = "http://***********:8066/portalrest/api/Account/Login/"
    login_payload = {
        "UserName": "pds_system",
        "Password": "pds123456"
    }

    login_response = requests.post(login_url, json=login_payload)
    if login_response.status_code != 200:
        raise HTTPException(status_code=500, detail="登录失败")

    token = login_response.json().get('token')
    if not token:
        raise HTTPException(status_code=500, detail="未获取到token")

    # 使用 token 获取数据
    data_url = "http://***********:8066/scadarest/api/ScriptCustomFunction/ExcuteCustomFunction"
    data_payload = {
        "id": "9a97411a-e853-4d94-a968-ae1d33ea615e",
        "projectId": "bf139c58-deb5-4f47-92d3-45ae2583db9e",
        "parameters": []
    }
    headers = {
        "isToken": 'false',
        "Authorization": f"Bearer {token}"
    }

    data_response = requests.post(data_url, json=data_payload, headers=headers)
    if data_response.status_code != 200:
        raise HTTPException(status_code=500, detail="数据请求失败")

    try:
        data = data_response.json()["Value"]["data"]
        states = data["states"]
        details_all = json.loads(data["details"][0]['data'])
        plc_data_all = data["plcData"]

        # 查找匹配机台号的内容
        details = next(item for item in details_all if item['deviceNo'] == device_no)
        plc_data = next(item for item in plc_data_all if item['deviceNo'] == device_no)

        return {
            "缓存位状态": states,
            "机台配置": details,
            "设备模式": plc_data
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理数据失败: {str(e)}")


@tcx_status_router.get("/wait_degumming")
def get_wait_degumming_info(area_code: str = Query(..., description="区域代码，例如 T_QPHC")):
    try:
        url = "http://***********:8066/admin-api/pds/config/scheduleStation/getByAreaCode"
        params = {"areaCode": area_code}

        response = requests.get(url, params=params)
        if response.status_code != 200:
            raise HTTPException(status_code=500, detail="数据请求失败")

        wait_degumming_info = response.json()["data"]
        wait_degumming_material = [x for x in wait_degumming_info if x["cutNo"]]

        parsed_material_info = []
        for material in wait_degumming_material:
            tmp = {
                "站点": material["stationName"],
                "刀号": material["cutNo"],
                "客户规格": f"{material['customerShortName']}-{material['spec']}-{material['thickness']}",
                "物料异常": material["resultDesc"],
                "状态": "待脱胶" if material["carStatus"] == 6 else "待取片",
                "是否被占用": "是" if material["status"] == 1 else "否"
            }
            parsed_material_info.append(tmp)

        return parsed_material_info

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理数据失败: {str(e)}")
