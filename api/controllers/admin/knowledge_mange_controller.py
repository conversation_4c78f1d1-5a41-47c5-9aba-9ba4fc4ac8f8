import os
import traceback
import uuid
from fastapi import APIRouter, UploadFile, File, Form, Request, BackgroundTasks
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.templating import Jinja2Templates
from pymilvus import MilvusClient

from core.knowledge_base.front_upload.document_parser import parse_document
from core.knowledge_base.front_upload.vector_storage import store_document_chunks
from core.config.logging import logger
# 导入文本分割器用于预览
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document

# 创建路由
upload_knowledge_router = APIRouter(prefix="/knowledge_mange", tags=["knowledge_mange"])
templates = Jinja2Templates(directory="templates")

# 上传文件的临时存储目录
UPLOAD_DIR = "cache/uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)


@upload_knowledge_router.get("/process", response_class=HTMLResponse)
async def get_file_process_page(request: Request):
    """文件处理页面"""
    return templates.TemplateResponse("knowledge_mange.html", {"request": request})


@upload_knowledge_router.get("/view", response_class=HTMLResponse)
async def get_file_process_page2(request: Request):
    """文件查看页面"""
    return templates.TemplateResponse("list_files.html", {"request": request})


@upload_knowledge_router.get("/list-collections")
async def list_collections():
    """获取Milvus数据库中的所有集合列表"""
    try:
        client = MilvusClient(uri="http://172.27.9.4:19530", token="root:Milvus", db_name="gc_knowledge")
        collections = client.list_collections()

        return JSONResponse(content={"success": True, "data": collections})
    except Exception as e:
        error_traceback = traceback.format_exc()
        logger.error(f"Error in /list-collections: {error_traceback}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e), "traceback": error_traceback}
        )


@upload_knowledge_router.delete("/delete-file")
async def delete_file(file_name: str, collection_name: str = "knowledge_20250303"):
    """删除文件"""
    try:
        client = MilvusClient(uri="http://172.27.9.4:19530", token="root:Milvus", db_name="gc_knowledge")
        res = client.delete(
            collection_name=collection_name,
            filter="file_name == '{}'".format(file_name)
        )
        logger.info(f"文件 {file_name} 已删除, 删除结果: {res}")
        return JSONResponse(content={"success": True, "data": res})
    except Exception as e:
        error_traceback = traceback.format_exc()
        logger.error(f"Error in /delete-file: {error_traceback}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e), "traceback": error_traceback}
        )


@upload_knowledge_router.get("/list-files")
async def list_files(collection_name: str = "knowledge_20250303"):
    """获取Milvus数据库中的文件列表

    Args:
        collection_name: 要查询的集合名称，默认为knowledge_20250303
    """
    try:
        client = MilvusClient(uri="http://172.27.9.4:19530", token="root:Milvus", db_name="gc_knowledge")
        iterator = client.query_iterator(
            collection_name=collection_name,  # 使用从前端传递的参数
            filter="",
            output_fields=["file_name", "category"],
            batch_size=100
        )

        grouped_files = {}  # 存储按类别分组的文件
        duplicate = set()

        while True:
            res = iterator.next()
            if len(res) == 0:
                iterator.close()
                break

            for item in res:
                file_name = item["file_name"]
                category = item["category"]

                if file_name not in duplicate:
                    if category not in grouped_files:
                        grouped_files[category] = []
                    grouped_files[category].append(file_name)
                    duplicate.add(file_name)

        return JSONResponse(content={"success": True, "data": grouped_files})

    except Exception as e:
        error_traceback = traceback.format_exc()
        logger.error(f"Error in /list-files: {error_traceback}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e), "traceback": error_traceback}
        )


@upload_knowledge_router.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """
    上传文件并解析为markdown
    """
    # 生成唯一文件名
    file_id = str(uuid.uuid4())
    file_extension = os.path.splitext(file.filename)[1]
    original_filename = file.filename  # 保存原始文件名
    temp_file_path = os.path.join(UPLOAD_DIR, f"{file_id}{file_extension}")

    # 保存上传的文件
    with open(temp_file_path, "wb") as f:
        content = await file.read()
        f.write(content)

    try:
        # 解析文件
        markdown_text = await parse_document(temp_file_path)

        return JSONResponse(content={
            "success": True,
            "file_id": file_id,
            "file_path": temp_file_path,
            "original_filename": original_filename,  # 返回原始文件名
            "markdown_text": markdown_text
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )


@upload_knowledge_router.post("/preview-chunks")
async def preview_chunks(
        text: str = Form(...),
        source: str = Form("")
):
    """
    预览文本块切割结果
    """
    try:
        # 文本分割器
        text_splitter = RecursiveCharacterTextSplitter(
            separators=["\n\n", "\n", " ", "", "####"],
            chunk_size=512,
            chunk_overlap=64
        )

        # 创建文档对象并分块
        document = Document(page_content=text, metadata={"source": source})
        chunks = text_splitter.split_documents([document])

        # 格式化切块结果
        chunks_data = []
        for idx, chunk in enumerate(chunks):
            chunks_data.append({
                "id": idx,
                "content": chunk.page_content,
                "metadata": chunk.metadata
            })

        return JSONResponse(content={
            "success": True,
            "chunks": chunks_data,
            "total_chunks": len(chunks)
        })
    except Exception as e:
        # 记录堆栈信息
        error_traceback = traceback.format_exc()
        logger.error(f"Error in /preview-chunks: {error_traceback}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e), "traceback": error_traceback}
        )


@upload_knowledge_router.post("/store-vector")
async def store_vector(
        markdown_text: str = Form(...),
        source: str = Form(...),
        original_filename: str = Form(""),  # 添加原始文件名参数
        category: str = Form(""),
        collection_name: str = Form("knowledge_20250303")  # 添加集合名称参数，默认值保持不变
):
    """
    将编辑后的markdown文本存入向量数据库

    Args:
        markdown_text: markdown格式的文本
        source: 文件来源
        original_filename: 原始文件名
        category: 类别
        collection_name: 集合名称，默认为"knowledge_20250303"
    """
    try:
        # 使用原始文件名作为存储名称
        file_source = original_filename if original_filename else os.path.basename(source)

        # 存入向量数据库
        result = store_document_chunks(
            text=markdown_text,
            source=file_source,  # 使用原始文件名
            category=category,
            collection_name=collection_name  # 传递集合名称参数
        )

        return JSONResponse(content={
            "success": True,
            "chunks_stored": result["total_chunks"],
            "message": f"文件 '{file_source}' 已成功处理并存入向量数据库 '{collection_name}'，共{result['total_chunks']}个文本块"
        })
    except Exception as e:
        # 记录堆栈信息
        error_traceback = traceback.format_exc()
        logger.error(f"Error in /store-vector: {error_traceback}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e), "traceback": error_traceback}
        )
