import datetime
from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates

# 创建首页路由
home_router = APIRouter(tags=["home"])
templates = Jinja2Templates(directory="templates")


@home_router.get("/", response_class=HTMLResponse)
async def home_page(request: Request):
    """首页 - 项目导航中心"""
    # 系统服务列表
    services = [
        {
            "id": "knowledge",
            "name": "知识库管理",
            "description": "上传、预览、管理知识库文档",
            "icon": "book",
            "color": "blue",
            "links": [
                {"url": "/knowledge_mange/process", "name": "文件处理", "icon": "file-upload"},
                {"url": "/knowledge_mange/view", "name": "文件查看", "icon": "list"},
            ]
        },
        {
            "id": "training_data",
            "name": "数据训练管理",
            "description": "管理模型的各类训练数据",
            "icon": "database",
            "color": "green",
            "links": [
                {"url": "/training/", "name": "SQL训练数据", "icon": "code"},
                {"url": "/training/bi/", "name": "BI数据可视化训练", "icon": "chart-bar"},
            ]
        },
        # 可以在这里添加更多服务模块
        {
            "id": "chat",
            "name": "对话管理",
            "description": "管理用户对话记录",
            "icon": "comments",
            "color": "purple",
            "links": [
                {"url": "/chat_history/", "name": "对话记录", "icon": "comment"},
            ]
        }
    ]

    # 获取当前年份
    current_year = datetime.datetime.now().year

    return templates.TemplateResponse("home.html", {
        "request": request,
        "services": services,
        "page_title": "系统服务中心",
        "current_year": current_year
    })