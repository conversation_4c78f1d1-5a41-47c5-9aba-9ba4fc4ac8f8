from fastapi import APIRouter, Form, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates

from core.text2sql.BI.BI_Vanna import BIVannaWithCorrection
from core.text2sql.model import Vanna

# 导入文本分割器用于预览

training_router = APIRouter(prefix="/training", tags=["training"])
templates = Jinja2Templates(directory="templates")

vn_with_correction = Vanna()


@training_router.get("/", response_class=HTMLResponse)
async def get_training_page(request: Request):
    print("访问训练页面")
    training_data = vn_with_correction.get_training_data()
    print(f"获取到训练数据: {len(training_data)} 条")
    # 确保DataFrame中的列名正确，并且数据可以被正确处理
    return templates.TemplateResponse("training.html", {"request": request, "training_data": training_data})


@training_router.post("/add")
async def add_training_data(
    request: Request,
    ddl: str = Form(None),
    documents_summary: str = Form(None),
    table_name: str = Form(None),
    question: str = Form(None),
    sql: str = Form(None)
):
    if ddl:
        print(f"添加DDL训练数据: {ddl[:50]}...")
        vn_with_correction.add_ddl(ddl=ddl, documents_summary=documents_summary, table_name=table_name)
    elif question and sql:
        print(f"添加问答训练数据. 问题: {question[:50]}..., SQL: {sql[:50]}...")
        vn_with_correction.add_question_sql(question=question, sql=sql)

    training_data = vn_with_correction.get_training_data()
    return templates.TemplateResponse("training.html", {
        "request": request,
        "training_data": training_data,
        "success_message": "数据添加成功"
    })


@training_router.post("/remove/{data_id}")
async def remove_training_data(request: Request, data_id: str):
    print(f"删除训练数据: {data_id}")
    vn_with_correction.remove_training_data(data_id)
    training_data = vn_with_correction.get_training_data()
    return templates.TemplateResponse("training.html", {"request": request, "training_data": training_data,
                                                        "success_message": f"数据 {data_id} 已删除"})
