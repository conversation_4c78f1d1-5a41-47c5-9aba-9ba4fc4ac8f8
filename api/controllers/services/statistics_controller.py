import time

from fastapi import Depends, APIRouter
from pymilvus import MilvusClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import Optional
import json

from core.config.database_config import kb_permission_map
from core.database.repositories import get_all_chats
from core.database.session import get_db

statistics_router = APIRouter(prefix="/statistics", tags=["statistics"])

RANK_CACHE = {
    "result": None,
    "timestamp": 0
}
RANK_CACHE_EXPIRE = 60 * 60  # 缓存60分钟（单位：秒）

@statistics_router.get("/qa_count")
def get_qa_statistics(
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        db: Session = Depends(get_db)
):
    """
    统计指定时间范围内的问答对数量

    - start_date: 开始日期（可选），格式为 YYYY-MM-DD
    - end_date: 结束日期（可选），格式为 YYYY-MM-DD
    """
    # 处理日期参数
    if end_date:
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        # 结束日期加1天，使查询包含结束当天的数据
        end_dt = end_dt + timedelta(days=1)
    else:
        end_dt = datetime.now()

    if start_date:
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    else:
        # 默认查询最近30天
        start_dt = end_dt - timedelta(days=30)

    # 获取所有聊天记录
    all_chats = get_all_chats(db)

    # 预处理聊天数据
    processed_chats = []
    for chat in all_chats:
        # 解析 message 字段
        message = json.loads(chat.message) if isinstance(chat.message, str) else chat.message
        # 提取消息类型和时间
        msg_type = message.get('type')
        if not message.get('data').get('content'):
            print(
                f"Warning: Message without content detected in session {chat.session_id} at {chat.created_at}")
            continue
        created_at = chat.created_at

        # 转换时间字符串为datetime对象
        if isinstance(created_at, str):
            created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))

        # 移除时区信息
        created_at = created_at.replace(tzinfo=None)

        # 筛选指定时间范围内的聊天
        if start_dt <= created_at < end_dt:
            processed_chats.append({
                'session_id': chat.session_id,
                'type': msg_type,
                'created_at': created_at
            })

    # 按会话ID和创建时间排序
    processed_chats.sort(key=lambda x: (x['session_id'], x['created_at']))

    # 统计问答对数（一个 human 跟一个 ai 紧挨着为一对）
    qa_pairs = []
    current_session = None
    last_type = None

    for chat in processed_chats:
        session = chat['session_id']
        msg_type = chat['type']
        created_date = chat['created_at'].date()

        if session != current_session:
            last_type = None
            current_session = session

        if last_type == 'human' and msg_type == 'ai':
            qa_pairs.append({
                'session_id': session,
                'date': created_date
            })

        last_type = msg_type

    # 计算总问答对数量
    total_qa_pairs = len(qa_pairs)

    # 按天统计数据
    daily_stats = {}
    for pair in qa_pairs:
        date_str = pair['date'].strftime('%Y-%m-%d')
        if date_str in daily_stats:
            daily_stats[date_str] += 1
        else:
            daily_stats[date_str] = 1

    # 将统计结果转换为列表格式
    daily_stats_list = [{"date": date, "count": count} for date, count in daily_stats.items()]
    daily_stats_list.sort(key=lambda x: x["date"])

    return {
        "total_qa_count": total_qa_pairs,
        "start_date": start_dt.strftime("%Y-%m-%d"),
        "end_date": (end_dt - timedelta(days=1)).strftime("%Y-%m-%d"),
        "daily_statistics": daily_stats_list
    }


@statistics_router.get("/knowledge_rank")
def get_knowledge_rank(
        k: int = 10
):
    now = time.time()
    # 如果缓存还有效，则直接返回缓存
    if RANK_CACHE["result"] and now - RANK_CACHE["timestamp"] < RANK_CACHE_EXPIRE:
        return RANK_CACHE["result"][:k]

    client = MilvusClient(uri="http://172.27.9.4:19530", token="root:Milvus", db_name="gc_knowledge")
    iterator = client.query_iterator(
        collection_name="knowledge_20250303",  # 使用从前端传递的参数
        filter="",
        output_fields=["file_name", "category"],
        batch_size=100
    )

    grouped_files = {}  # 存储按类别分组的文件
    duplicate = set()

    while True:
        res = iterator.next()
        if len(res) == 0:
            iterator.close()
            break

        for item in res:
            file_name = item["file_name"]
            category = item["category"]

            if file_name not in duplicate:
                if category not in grouped_files:
                    grouped_files[category] = []
                grouped_files[category].append(file_name)
                duplicate.add(file_name)

    kb_permission_map_reverse = {v: k for k, v in kb_permission_map.items() if k != "知识库-管理员"}
    grouped_files_count = {}
    for key, value in grouped_files.items():
        if key in kb_permission_map_reverse:
            if kb_permission_map_reverse[key] not in grouped_files_count:
                grouped_files_count[kb_permission_map_reverse[key]] = 0
            grouped_files_count[kb_permission_map_reverse[key]] += len(value)

    sorted_grouped_files_count = sorted(grouped_files_count.items(), key=lambda x: x[1], reverse=True)[:k]

    RANK_CACHE["result"] = sorted_grouped_files_count
    RANK_CACHE["timestamp"] = now

    return sorted_grouped_files_count