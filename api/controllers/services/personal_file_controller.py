import asyncio

import httpx
from fastapi import Depends, APIRouter, HTTPException, status
from pymilvus import MilvusClient
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
import os
import traceback
from api.schemas.chat import UploadRequest
from core.config.database_config import kb_permission_map
from core.config.logging import logger
from core.database.repositories import get_personal_knowledge, add_personal_knowledge, update_file_flag, \
    mark_file_as_deleted, update_file_content, get_es_person_job_number, update_file_domain
from core.database.session import get_db_es, get_db, SessionLocal, SessionLocalES
from core.knowledge_base.personal_kb import upload_file_to_vector_db
from fastapi import BackgroundTasks

file_router = APIRouter(tags=["file"])


@file_router.post("/files/upload")
async def upload_files(
        background_tasks: BackgroundTasks,
        request_data: UploadRequest
):
    user_id = request_data.userId.strip()
    file_url = request_data.fileUrl.strip()
    file_id = request_data.fileId
    file_name = request_data.filName

    domain = request_data.domain

    logger.info(f"用户 {user_id} 文件名: {file_name} 上传文件 {file_url} 文件ID: {file_id}")

    response_data = {"message": "文件上传已接收，处理中", "file_id": file_id}

    if domain == "知识库-项目管理部组会纪要" or domain == "知识库-研发中心项目月会纪要":
        background_tasks.add_task(process_meeting_minutes, file_url, file_id, file_name)
    else:
        # 将文件处理任务添加到后台任务
        if domain:
            background_tasks.add_task(process_department_upload, domain, file_url, file_id, file_name)
        else:
            background_tasks.add_task(process_personal_upload, user_id, file_url, file_id, file_name)

    return response_data


async def process_meeting_minutes(file_url, file_id, file_name_no_ext):
    upload_api = "http://**********:9632/documents/upload"
    status_api = "http://**********:9632/documents"

    # 1. 先下载文件
    tmp_download_path = None
    try:
        async with httpx.AsyncClient(timeout=60) as client:
            resp = await client.get(file_url)
            resp.raise_for_status()
            content_disposition = resp.headers.get("content-disposition", "")
            ext = ""
            # 1.1 获取原始后缀
            if "filename=" in content_disposition:
                original_filename = content_disposition.split("filename=")[1].strip('\"')
                _, ext = os.path.splitext(original_filename)
            else:
                # 从url解析
                from urllib.parse import urlparse
                parsed_url = urlparse(file_url)
                _, ext = os.path.splitext(parsed_url.path)
            if not ext:
                ext = ".dat"  # fallback

            local_filename = f"{file_name_no_ext}{ext}"
            tmp_download_path = os.path.join("/tmp", local_filename)  # Linux系统建议用 /tmp
            with open(tmp_download_path, "wb") as f:
                f.write(resp.content)
            logger.info(f"[DOWNLOAD] 文件已下载到: {tmp_download_path}")

        # 2. 上传到文档系统
        async with httpx.AsyncClient(timeout=120) as client:
            with open(tmp_download_path, "rb") as f:
                files = {'file': (local_filename, f, 'application/octet-stream')}
                resp = await client.post(upload_api, files=files)
                resp.raise_for_status()
                logger.info(f"[UPLOAD] 文件上传接口响应: {resp.json()}")

    except Exception as e:
        logger.error(f"[UPLOAD] 文件下载/上传异常: {e}")
        if tmp_download_path and os.path.exists(tmp_download_path):
            os.remove(tmp_download_path)
        return

    # 3. 清理本地临时文件
    if tmp_download_path and os.path.exists(tmp_download_path):
        os.remove(tmp_download_path)

    # 4. 轮询解析状态
    max_wait_minutes = 30
    interval_seconds = 60
    max_loops = max_wait_minutes * 60 // interval_seconds
    for i in range(int(max_loops)):
        await asyncio.sleep(interval_seconds)
        try:
            async with httpx.AsyncClient(timeout=20) as client:
                status_resp = await client.get(status_api)
                status_resp.raise_for_status()
                data = status_resp.json()
                all_status = data.get("statuses", {})
                processed = all_status.get("processed", [])
                failed = all_status.get("failed", [])

                if any(doc["file_path"] == local_filename for doc in processed):
                    logger.info(f"[STATUS] 文件 {file_id} 已处理完成（第 {i+1} 次轮询）")
                    try:
                        new_db_session = SessionLocalES()
                        update_file_flag(new_db_session, file_id, 2)
                    except Exception as ex:
                        logger.error(f"更新文件状态失败: {str(ex)}")
                    finally:
                        new_db_session.close()
                    return

                if any(doc["file_path"] == local_filename for doc in failed):
                    try:
                        new_db_session = SessionLocalES()
                        update_file_flag(new_db_session, file_id, 1)
                    except Exception as ex:
                        logger.error(f"[STATUS] 文件 {file_id} 处理失败（第 {i+1} 次轮询）: {str(ex)}")
                    finally:
                        new_db_session.close()
                    return

                logger.info(f"[STATUS] 文件 {file_id} 未完成，当前第 {i+1} 次轮询，继续等待...")

        except Exception as e:
            logger.error(f"[STATUS] 查询文档状态异常（第 {i+1} 次轮询）: {e}")
    logger.warning(f"[TIMEOUT] 文件 {file_id} 处理超时（等待约 {max_wait_minutes} 分钟）")


async def process_department_upload(domain, file_url, file_id, file_name):
    """异步处理上传的文件"""
    try:
        kb_id = "knowledge_20250303"
        domain2id = kb_permission_map.get(domain, "")
        if not domain2id:
            logger.error(f"域名 {domain} 没有对应的ID，文件 {file_id} 处理失败")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文件上传失败")

        new_db_session = SessionLocalES()
        try:
            # 文件解析，存入向量数据库
            markdown_text = await upload_file_to_vector_db(file_url, kb_id, file_id, file_name, domain=domain2id)

            update_file_flag(new_db_session, file_id, 2)  # 2 表示成功
            update_file_content(new_db_session, file_id, markdown_text[:5000])
            logger.info(f"文件 {file_id} 处理完成")
        except Exception as e:
            logger.error(f"文件处理失败: {str(e)}")
            update_file_flag(new_db_session, file_id, 1)  # 1 表示失败
            raise
        finally:
            new_db_session.close()

    except SQLAlchemyError as e:
        logger.error(f"数据库操作失败: {str(e)}")
        # 创建新的会话来更新状态
        try:
            new_session = SessionLocalES()
            update_file_flag(new_session, file_id, 1)
            new_session.close()
        except Exception as ex:
            logger.error(f"更新文件状态失败: {str(ex)}")
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        try:
            new_session = SessionLocalES()
            update_file_flag(new_session, file_id, 1)
            new_session.close()
        except Exception as ex:
            logger.error(f"更新文件状态失败: {str(ex)}")


async def process_personal_upload(user_id, file_url, file_id, file_name):
    """异步处理上传的文件"""
    db_es = SessionLocalES()
    db = SessionLocal()
    try:
        data = get_personal_knowledge(db=db, user_id=user_id)
        if not data:
            kb_id = user_id + "collection_db"
            add_personal_knowledge(db=db, user_id=user_id, kb_id=kb_id)
        else:
            kb_id = data[0].kb_id

        # 文件解析，存入向量数据库
        markdown_text = await upload_file_to_vector_db(file_url, kb_id, file_id, file_name, domain="Personal")

        update_file_flag(db_es, file_id, 2)
        update_file_content(db_es, file_id, markdown_text[:5000])
        logger.info(f"文件 {file_id} 处理完成")

    except SQLAlchemyError as e:
        logger.error(f"数据库操作失败: {str(e)}")
        db.rollback()
        db_es.rollback()
        update_file_flag(db_es, file_id, 1)
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")  # Log full traceback
        update_file_flag(db_es, file_id, 1)
    finally:
        db.close()
        db_es.close()


@file_router.delete("/files/{file_id}")
def delete_file(file_id: int, db_es: Session = Depends(get_db_es)):
    try:
        res = mark_file_as_deleted(db_es, file_id)
        if not res:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文件不存在")
        else:
            logger.info(f"文件 {file_id} 已标记为已删除")

        user_id, domain = get_es_person_job_number(db_es, file_id)
        logger.info(f"用户 {user_id} 删除文件 {file_id}")
        # 删除向量数据库中的文件
        client = MilvusClient(uri="http://**********:19530", token="root:Milvus", db_name="gc_knowledge")
        if domain:
            res = client.delete(
                collection_name=f"knowledge_20250303",
                filter=f"file_id == '{file_id}'"
            )
        else:
            res = client.delete(
                collection_name=f"{user_id}collection_db",
                filter=f"file_id == '{file_id}'"
            )
        logger.info(f"删除向量数据库中的文件: {res}")
        return {"message": "文件删除成功"}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"删除文件失败: {str(e)}")


@file_router.post("/files/migrate")
def migrate_files(
        background_tasks: BackgroundTasks,
        request_data: UploadRequest,
        db: Session = Depends(get_db),
        db_es: Session = Depends(get_db_es),
):
    user_id = request_data.userId.strip()
    file_url = request_data.fileUrl.strip()
    file_id = request_data.fileId
    file_name = request_data.filName
    domain = request_data.domain

    logger.info(f"用户 {user_id} 开始迁移文件 {file_name} 到部门知识库 {domain}")

    # 先返回成功响应
    response_data = {"message": "文件迁移已接收，处理中", "file_id": file_id}

    # 将迁移任务添加到后台任务
    background_tasks.add_task(process_migration, user_id, domain, file_url, file_id, file_name, db, db_es)

    return response_data


def process_migration(user_id, domain, file_url, file_id, file_name, db, db_es):
    """异步处理文件迁移"""
    try:
        # 1. 从个人知识库中删除文件
        client = MilvusClient(uri="http://**********:19530", token="root:Milvus", db_name="gc_knowledge")
        res = client.delete(
            collection_name=f"{user_id}collection_db",
            filter=f"file_id == '{file_id}'"
        )
        logger.info(f"从个人知识库删除文件: {res}")

        # 2. 将文件添加到部门知识库
        kb_id = "knowledge_20250303"
        domain2id = kb_permission_map.get(domain, "")
        if not domain2id:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="部门不存在")

        # 文件解析，存入向量数据库
        markdown_text = upload_file_to_vector_db(file_url, kb_id, file_id, file_name, domain=domain2id)

        # 更新文件状态和内容
        update_file_flag(db_es, file_id, 2)
        update_file_domain(db_es, file_id, domain)
        # update_file_content(db_es, file_id, markdown_text[:5000])
        logger.info(f"文件 {file_id} 迁移完成")

    except SQLAlchemyError as e:
        logger.error(f"数据库操作失败: {str(e)}")
        db.rollback()
        db_es.rollback()
        update_file_flag(db_es, file_id, 1)
    except Exception as e:
        logger.error(f"文件迁移失败: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        update_file_flag(db_es, file_id, 1)


if __name__ == '__main__':
    import uuid
    import asyncio

    asyncio.run(
        process_meeting_minutes(
            file_url="http://172.27.9.18:48082/admin-api/infra/file/1/get/b2241f3525bb4afaa2ff344a76d62de7.pdf",
            file_id=str(uuid.uuid4()),
            file_name_no_ext="【20250117】研发中心1月项目月会会议纪要VF"
        )
    )