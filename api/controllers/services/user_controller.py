from fastapi import Depends, APIRouter
from sqlalchemy.orm import Session

from core.database.repositories import get_user_chat_map, get_chat_history, delete_user_chat
from core.database.session import get_db

user_router = APIRouter(tags=["chat_info"])


@user_router.get("/users/{user_id}")
def read_user(user_id: str, db: Session = Depends(get_db)):
    result = get_user_chat_map(db=db, user_id=user_id)
    if not result:
        return []

    json_data = []
    for r in result:
        _uuid = r.session_id
        chat_history = get_chat_history(db=db, session_id=_uuid)
        if chat_history:
            decs = chat_history[0].message["data"]["content"][:20].replace("/no_think", "").replace("/think", "")
            json_data.append({"uuid": str(_uuid), "desc": decs})
    return json_data


@user_router.get("/session/{session_id}")
def read_session(session_id: str, db: Session = Depends(get_db)):
    result = get_chat_history(db=db, session_id=session_id)
    if not result:
        return []

    json_data = []
    for r in result:
        decs = r.message["data"]["content"]
        typ = r.message["data"]["type"]
        if "query" in decs and "fig" in decs:
            decs = eval(decs)
            json_data.append({"content": decs["query"], "type": typ, "fig": decs["fig"]})
        elif "query" in decs and "url" in decs:
            decs = eval(decs)
            json_data.append({"content": decs["query"], "type": typ, "url": decs["url"]})
        else:
            json_data.append({"content": decs, "type": typ, "fig": ""})

    return json_data


@user_router.delete("/session/{session_id}")
def delete_session(session_id: str, db: Session = Depends(get_db)):
    delete_user_chat(db=db, session_id=session_id)
    return {"status": "success", "message": f"会话 {session_id} 已成功删除"}
