from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIRouter
from starlette.requests import Request

from api.schemas.chat import QueryRequest, SliceMchQueryRequest
from api.utils.utils import get_user_roles_by_user_id
from core.config.logging import logger
from core.services.query_processor import handle_user_query, process_slice_mch

chat_router = APIRouter(tags=["chat"])


@chat_router.post("/api/chat")
async def chat(request_data: QueryRequest, request: Request):
    query = request_data.messages.strip()
    session_id = request_data.uuid.strip()
    think = request_data.deepSeek
    user_id = request_data.userId.strip()
    # roles = request_data.roles
    roles = get_user_roles_by_user_id(user_id)

    sql = request_data.sql
    knowledge = request_data.knowledge
    person = request_data.person

    bi = request_data.dataBI

    dify_agv = request_data.dify_agv
    dify_slot_recommend = request_data.dify_slot_recommend
    dify_project_manner = request_data.dify_project_manner
    dify_person_eval = request_data.dify_person_eval
    dify_meeting_minutes = request_data.dify_meeting_minutes

    logger.info(
        f"接受到请求  - user_id: {user_id}, query: {query}"
    )

    try:
        return await handle_user_query(query, session_id, think, user_id, sql, knowledge, person, roles, request, bi, dify_agv, dify_slot_recommend, dify_project_manner, dify_person_eval, dify_meeting_minutes)

    except ValueError as e:
        logger.error(f"Chat request failed - session_id: {session_id}, error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@chat_router.post("/api/chat_slice")
async def chat_slicemch(request_data: SliceMchQueryRequest, request: Request):
    label = request_data.label.strip()
    value = request_data.value.strip()
    user_id = request_data.userId.strip()
    roles = request_data.roles
    machine_no = request_data.machineNo.strip()

    logger.info(
        f"Received slice machine chat request - user_id: {user_id}, label: {label}, "
        f"value: {value}, machine_no: {machine_no}, roles: {roles}")

    try:
        return await process_slice_mch(label, value, user_id, roles, machine_no, request)

    except ValueError as e:
        logger.error(f"Slice machine chat request failed - user_id: {user_id}, error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
