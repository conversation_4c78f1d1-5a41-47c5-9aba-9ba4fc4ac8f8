from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from starlette.staticfiles import StaticFiles

from api.api import router as api_router

app = FastAPI(
    title="高测天机平台后端服务",
    description="提供知识库检索、联网搜索和深度思考功能的大模型服务",
    version="2.0"
)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# app.mount("/static", StaticFiles(directory="static"), name="static")

app.include_router(api_router)

# 调试时使用
if __name__ == "__main__":
    print("服务启动中")
    import uvicorn
    # 开发环境的配置
    uvicorn.run(
        "app:app",  # 假设这个文件名为main.py
        host="0.0.0.0",
        port=59990,
        reload=False,
        workers=1
    )

# 生产环境使用
#  uvicorn app:app --host 0.0.0.0 --por t 59990 --workers 4
#  nohup gunicorn app:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:59990 > logs/nohup.out 2>&1 &
#  pkill -9 gunicorn