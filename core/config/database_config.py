import psycopg
from sqlalchemy import create_engine

conn_info = "host=********** port=5432 dbname=chat_history user=postgres password=123456"
sync_connection = psycopg.connect(conn_info)

# 历史记录表
CHAT_HISTORY_DATABASE_URL = 'postgresql+psycopg2://postgres:123456@**********:5432/chat_history'
# ES个人知识库
ES_DATABASE_URL = 'postgresql+psycopg2://postgres:cdData01_PG0419@***********:5432/gc_es'

cw_engine = create_engine(
    "postgresql+psycopg2://gccw:GcW%400319@***********:5432/gc_cw",
    pool_size=5,
    max_overflow=10,
    pool_pre_ping=True
)


def get_cw_conn():
    conn = cw_engine.connect()
    try:
        yield conn
    finally:
        conn.close()


# 知识库权限映射表
kb_permission_map = {
    "知识库-分选": "FX",
    "知识库-切片": "QP",
    "知识库-脱插洗": "TCX",
    "知识库-研究一室": "LIMS",
    "知识库-EAM": "EAM",
    "知识库-ES": "ES",
    "知识库-粘胶": "ZJ",
    "知识库-原料": "YL",
    "知识库-盐城技术": "YC",
    "知识库-光伏装备应用研发": "ZBYC",
    "知识库-自动化培训": "ZDHPX",
    "知识库-设备": "SB",
    "知识库-设施": "SS",
    "知识库-装备技术研发": "ZBYF",
    "知识库-电气研发": "DQYF",
    "知识库-研发导入": "YFDR",
    "知识库-软件": "RJ",
    "知识库-算法": "SF",
    "知识库-研发测试": "YFCS",
    "知识库-项目管理": "XMGL",
    "知识库-工具研发": "GJYF",
    "知识库-研发公共": "YFGG",
    "知识库-切片公共": "QPGG",
    "知识库-切片产品研发": "QPCPYF",
    "知识库-IT管理部": "IT",
    "知识库-项目管理部组会纪要": "XMGLJY",
    "知识库-研发中心项目月会纪要": "YFJY",
    "知识库-机加产品研发": "JJCP",

    "知识库-管理员": ["FX", "QP", "TCX", "LIMS", "EAM", "ES", "ZJ", "YL", "YC", "ZBYC", "AGV", "SB", "SS",
                      "ZBYF", "DQYF", "YFDR", "RJ", "SF", "YFCS", "XMGL", "GJYF", "YFGG", "QPGG", "QPCPYF", "ZDHPX",
                      "IT",
                      "XMGLJY", "YFJY", "JJCP"],
}

# 数据检索权限映射表
table_permission_map = {
    "知识库-数据检索-水质检测": ["gaoce_lims_water_inspection", "lims_smallloop_item_limit"],
    "知识库-数据检索-人员出勤": ["pds_attend_record"],
    "知识库-数据检索-良率产量": ["mv_pds_all_round_report"],
    "知识库-数据检索-断线记录": ["mv_pds_all_round_report"],
    "知识库-数据检索-管理员": ["gaoce_lims_water_inspection", "lims_smallloop_item_limit", "pds_attend_record",
                               "mv_pds_all_round_report"],

    "知识库-BI检索-技术成果": ["ods_fs_yf_technology_result"],
    "知识库-BI检索-项目管理": ["vw_project_solution", "dwd_rd_project_subject_information_table", "vw_project_cost",
                               "vw_project_cost_solution"]
}

assistant_permission_mapping = {
    "切片知识助手": ["QP"],
    "清洗分选知识助手": ["TCX", "FX"],
    "原料&粘胶知识助手": ["YL", "ZJ"],
    "装备研发知识助手": ["QPCPYF", "ZBYC", "DQYF", "ZBYF"],
    "设施知识助手": ["SS"],
    "设备知识助手": ["SB"],
    "项目管理知识助手": ["XMGL"],
    "软件&算法知识助手": ["RJ", "SF"],
    "研发导入知识助手": ["YFDR"],
    "研发测试知识助手": ["YFCS"],
    "工具研发知识助手": ["GJYF"],
    "材料研究知识助手": ["LIMS"],
}
