from datetime import datetime
from sqlalchemy import Column, UUID, VARCHAR, JSON, INT, TIMESTAMP, Boolean, BigInteger, String, Integer, Text

from core.database.base import Base
from sqlalchemy.sql import func

class UserChatMap(Base):
    __tablename__ = "employee_session_uuid_map"

    session_id = Column(UUID, primary_key=True, index=True)
    user_id = Column(VARCHAR, nullable=True)
    create_time = Column(TIMESTAMP, nullable=False)
    is_deleted = Column(Boolean, default=False)
    conversation_id = Column(UUID, nullable=False)
    assistant_name = Column(VARCHAR, nullable=False)

    def __repr__(self):
        return f"<UserChatMap(session_id={self.session_id}, user_id={self.user_id})>"


class ChatHistory(Base):
    __tablename__ = "chat_history"

    id = Column(INT, primary_key=True, index=True)
    session_id = Column(UUID, nullable=True)
    message = Column(JSON, nullable=True)
    created_at = Column(TIMESTAMP, nullable=False)

    def __repr__(self):
        return f"<ChatHistory(id={self.id}, session_id={self.session_id})>"


class PersonalKnowledge(Base):
    __tablename__ = "personal_knowledge"

    user_id = Column(VARCHAR, nullable=True, primary_key=True)
    kb_id = Column(VARCHAR, nullable=True)
    kb_name = Column(VARCHAR, nullable=False)

    def __repr__(self):
        return f"<PersonalKnowledge(user_id={self.user_id}, kb_id={self.kb_id})>"


class EsPersonFile(Base):
    __tablename__ = "es_person_knowledge_file_v2"

    id = Column(BigInteger, primary_key=True, comment="自增编号")
    job_number = Column(String(255), nullable=False, comment="用户工号")
    file_id_uuid = Column(UUID, nullable=False, comment="文件UUID")
    creator = Column(String(64), comment="创建者")
    create_time = Column(TIMESTAMP, comment="创建时间")
    update_time = Column(TIMESTAMP, default=func.now(), onupdate=func.now(), comment="更新时间")
    deleted = Column(Integer, nullable=False, default=0, comment="是否删除")
    tenant_id = Column(BigInteger, nullable=False, default=0, comment="租户编号")
    url = Column(String(255), nullable=False, comment="文件链接")
    name = Column(String(255), comment="文件名")
    flag = Column(Integer, comment="解析状态 0 解析中 1解析失败 2解析成功")
    content = Column(Text, comment="文件内容")  # 新增字段
    domain = Column(String(255), comment="文件领域")
    file_id = Column(BigInteger, comment="文件ID")

    def __repr__(self):
        return f"<EsPersonFile(id={self.id}, job_number={self.job_number}, file_id={self.file_id}, name={self.name})>"
