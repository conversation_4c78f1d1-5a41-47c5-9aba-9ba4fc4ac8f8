from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from core.config.database_config import CHAT_HISTORY_DATABASE_URL, ES_DATABASE_URL

# 创建引擎
engine = create_engine(
    CHAT_HISTORY_DATABASE_URL,
    echo=False,
    pool_size=100,
    max_overflow=500,
    pool_timeout=60
)

engine_es = create_engine(
    ES_DATABASE_URL,
    echo=False,
    pool_size=100,
    max_overflow=500,
    pool_timeout=60
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
SessionLocalES = sessionmaker(autocommit=False, autoflush=False, bind=engine_es)


# 依赖注入：获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_db_es():
    db = SessionLocalES()
    try:
        yield db
    finally:
        db.close()
