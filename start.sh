#!/bin/bash

# 加载 conda 环境
echo "正在加载 conda 环境..."
source /home/<USER>/envs/algo/anaconda3/etc/profile.d/conda.sh
conda activate es_llm
echo "conda 环境 'es_llm' 已激活"

# 启动 vanna_backstage_server.py
echo "正在启动 vanna_backstage_server.py..."
nohup python vanna_backstage_server.py > logs/vanna_backstage_server.log 2>&1 &
echo "vanna_backstage_server.py 已启动，日志输出到 logs/vanna_backstage_server.log"

# 启动 bi_vanna_backstage_server.py
echo "正在启动 bi_vanna_backstage_server.py..."
nohup python bi_vanna_backstage_server.py > logs/bi_vanna_backstage_server.log 2>&1 &
echo "bi_vanna_backstage_server.py 已启动，日志输出到 logs/bi_vanna_backstage_server.log"

# 启动 gunicorn 主服务
echo "正在启动 Gunicorn 主服务..."
nohup gunicorn app:app -w 16 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:59990 --timeout 1200 > logs/nohup.out 2>&1 &
echo "Gunicorn 主服务已启动，PID: $!，监听地址: http://0.0.0.0:59990"

# 最终提示信息
echo "所有服务已启动完成"